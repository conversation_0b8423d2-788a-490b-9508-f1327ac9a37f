<?php
/**
 * Fix Customer Name - Separate Full Name into First and Last Name
 * This script updates a specific booking to properly separate the customer name
 */

// Include database connection
include('includes/config.php');

// Check if booking code is provided
if (isset($_GET['booking_code'])) {
    $booking_code = $_GET['booking_code'];
    
    // Get the booking details
    $query = "SELECT booking_id, first_name, last_name FROM bookings WHERE booking_code = ?";
    $stmt = $con->prepare($query);
    $stmt->bind_param("s", $booking_code);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $booking = $result->fetch_assoc();
        
        echo "<h2>Current Booking Details</h2>";
        echo "<p><strong>Booking ID:</strong> " . $booking['booking_id'] . "</p>";
        echo "<p><strong>Current First Name:</strong> " . ($booking['first_name'] ?: 'Not set') . "</p>";
        echo "<p><strong>Current Last Name:</strong> " . ($booking['last_name'] ?: 'Not set') . "</p>";
        
        // Show form to update names
        echo '<form method="POST" action="">
                <input type="hidden" name="booking_id" value="' . $booking['booking_id'] . '">
                <div style="margin: 20px 0;">
                    <label for="first_name">First Name:</label><br>
                    <input type="text" id="first_name" name="first_name" value="Carlos" style="padding: 5px; margin: 5px 0; width: 200px;">
                </div>
                <div style="margin: 20px 0;">
                    <label for="last_name">Last Name:</label><br>
                    <input type="text" id="last_name" name="last_name" value="Reyes" style="padding: 5px; margin: 5px 0; width: 200px;">
                </div>
                <button type="submit" name="update_names" style="padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer;">
                    Update Names
                </button>
              </form>';
    } else {
        echo "<p style='color: red;'>Booking not found with code: " . htmlspecialchars($booking_code) . "</p>";
    }
    
    $stmt->close();
} else {
    echo "<p>Please provide a booking code. Example: <a href='?booking_code=BOOKING-20250126-78901'>?booking_code=BOOKING-20250126-78901</a></p>";
}

// Handle form submission
if (isset($_POST['update_names'])) {
    $booking_id = intval($_POST['booking_id']);
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    
    if (!empty($first_name) && !empty($last_name)) {
        // Update the booking with separated names
        $update_query = "UPDATE bookings SET first_name = ?, last_name = ? WHERE booking_id = ?";
        $update_stmt = $con->prepare($update_query);
        $update_stmt->bind_param("ssi", $first_name, $last_name, $booking_id);
        
        if ($update_stmt->execute()) {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; margin: 20px 0; border: 1px solid #c3e6cb; border-radius: 4px;'>
                    <strong>Success!</strong> Customer name updated successfully.<br>
                    First Name: " . htmlspecialchars($first_name) . "<br>
                    Last Name: " . htmlspecialchars($last_name) . "
                  </div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 20px 0; border: 1px solid #f5c6cb; border-radius: 4px;'>
                    <strong>Error!</strong> Failed to update customer name: " . $update_stmt->error . "
                  </div>";
        }
        
        $update_stmt->close();
    } else {
        echo "<div style='background: #fff3cd; color: #856404; padding: 10px; margin: 20px 0; border: 1px solid #ffeaa7; border-radius: 4px;'>
                <strong>Warning!</strong> Both first name and last name are required.
              </div>";
    }
}

$con->close();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Fix Customer Name</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fix Customer Name Tool</h1>
        <p>This tool helps you separate full customer names into first and last names for existing bookings.</p>
    </div>
</body>
</html>
