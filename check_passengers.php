<?php
// Check passenger data in database
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'booking_system';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔍 CHECKING PASSENGER DATA...\n\n";
    
    // Check if booking_passengers table exists and has data
    $count = $pdo->query("SELECT COUNT(*) FROM booking_passengers")->fetchColumn();
    echo "📊 Total passengers in booking_passengers table: $count\n\n";
    
    if ($count > 0) {
        echo "📋 Sample passenger data:\n";
        $passengers = $pdo->query("SELECT * FROM booking_passengers LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($passengers as $passenger) {
            echo "- Booking ID: {$passenger['booking_id']}, Name: {$passenger['first_name']} {$passenger['last_name']}, Type: {$passenger['passenger_type']}\n";
        }
    } else {
        echo "❌ NO PASSENGER DATA FOUND!\n";
        echo "🔧 This means the booking form is not saving passenger details to booking_passengers table.\n\n";
        
        // Check bookings table for passenger info
        echo "📋 Checking bookings table for passenger data:\n";
        $bookings = $pdo->query("SELECT booking_id, booking_code, no_of_pax, passenger_details FROM bookings LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
        foreach ($bookings as $booking) {
            echo "- Booking: {$booking['booking_code']}, Passengers: {$booking['no_of_pax']}\n";
            if (!empty($booking['passenger_details'])) {
                echo "  Passenger Details: " . substr($booking['passenger_details'], 0, 100) . "...\n";
            } else {
                echo "  ❌ No passenger_details data\n";
            }
        }
    }
    
    echo "\n🔍 CHECKING BOOKING FORM SUBMISSION PROCESS...\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}
?>
