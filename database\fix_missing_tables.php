<?php
/**
 * Fix missing tables and columns
 * Run this to fix "Table doesn't exist" errors
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'booking_system';

try {
    // Connect to MySQL
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // 1. Create destinations table if not exists
    echo "Creating destinations table...\n";
    $sql = "CREATE TABLE IF NOT EXISTS `destinations` (
        `destination_id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `description` text DEFAULT NULL,
        `location` varchar(255) DEFAULT NULL,
        `price_per_person` decimal(10,2) DEFAULT 0.00,
        `environmental_fee` decimal(10,2) DEFAULT 100.00,
        `status` enum('active','inactive') DEFAULT 'active',
        `created_at` datetime DEFAULT current_timestamp(),
        `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`destination_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($sql);
    echo "✅ Destinations table created/verified.\n";
    
    // 2. Insert default destinations if table is empty
    $count = $pdo->query("SELECT COUNT(*) FROM destinations")->fetchColumn();
    if ($count == 0) {
        echo "Inserting default destinations...\n";
        $sql = "INSERT INTO `destinations` (`destination_id`, `name`, `description`, `location`, `price_per_person`, `environmental_fee`, `status`) VALUES 
        (1, 'Gigantes Island', 'Beautiful island destination with crystal clear waters and white sand beaches', 'Carles, Iloilo', 0.00, 100.00, 'active'),
        (2, 'Antonia Beach', 'Pristine beach with excellent snorkeling spots', 'Carles, Iloilo', 0.00, 100.00, 'active'),
        (3, 'Cabugao Gamay Island', 'Small island perfect for day trips and swimming', 'Carles, Iloilo', 0.00, 100.00, 'active'),
        (4, 'Tangke Saltwater Lagoon', 'Natural saltwater lagoon with unique rock formations', 'Carles, Iloilo', 0.00, 100.00, 'active'),
        (5, 'Bantigue Sand Bar', 'Stunning sandbar that appears during low tide', 'Carles, Iloilo', 0.00, 100.00, 'active')";
        
        $pdo->exec($sql);
        echo "✅ Default destinations inserted.\n";
    } else {
        echo "✅ Destinations already exist ($count records).\n";
    }
    
    // 3. Create booking_passengers table if not exists
    echo "Creating booking_passengers table...\n";
    $sql = "CREATE TABLE IF NOT EXISTS `booking_passengers` (
        `passenger_id` int(11) NOT NULL AUTO_INCREMENT,
        `booking_id` int(11) NOT NULL,
        `first_name` varchar(100) NOT NULL,
        `last_name` varchar(100) NOT NULL,
        `age` int(11) NOT NULL,
        `sex` enum('Male','Female') NOT NULL,
        `complete_address` text DEFAULT NULL,
        `contact_number` varchar(20) DEFAULT NULL,
        `passenger_type` enum('main_booker','companion','regular','discounted','children','infants') DEFAULT 'companion',
        `is_main_contact` tinyint(1) DEFAULT 0,
        `created_at` datetime DEFAULT current_timestamp(),
        PRIMARY KEY (`passenger_id`),
        KEY `booking_id` (`booking_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    $pdo->exec($sql);
    echo "✅ Booking passengers table created/verified.\n";
    
    // 4. Add missing columns to bookings table
    echo "Adding missing columns to bookings table...\n";
    
    $columns_to_add = [
        'suffix' => "ALTER TABLE `bookings` ADD COLUMN `suffix` varchar(10) DEFAULT NULL AFTER `last_name`",
        'total_passengers' => "ALTER TABLE `bookings` ADD COLUMN `total_passengers` int(11) DEFAULT 1 AFTER `no_of_pax`",
        'payment_proof' => "ALTER TABLE `bookings` ADD COLUMN `payment_proof` varchar(255) DEFAULT NULL AFTER `payment_method`",
        'is_today_booking' => "ALTER TABLE `bookings` ADD COLUMN `is_today_booking` tinyint(1) DEFAULT 0 AFTER `booking_status`",
        'passenger_details' => "ALTER TABLE `bookings` ADD COLUMN `passenger_details` longtext DEFAULT NULL AFTER `destination_id`"
    ];
    
    foreach ($columns_to_add as $column => $sql) {
        try {
            // Check if column exists
            $check = $pdo->query("SHOW COLUMNS FROM bookings LIKE '$column'")->rowCount();
            if ($check == 0) {
                $pdo->exec($sql);
                echo "✅ Added column: $column\n";
            } else {
                echo "✅ Column already exists: $column\n";
            }
        } catch (PDOException $e) {
            echo "⚠️ Error adding column $column: " . $e->getMessage() . "\n";
        }
    }
    
    // 5. Update existing bookings to have default destination_id if NULL
    echo "Updating existing bookings...\n";
    $updated = $pdo->exec("UPDATE `bookings` SET `destination_id` = 1 WHERE `destination_id` IS NULL OR `destination_id` = 0");
    echo "✅ Updated $updated bookings with default destination.\n";
    
    // 6. Auto-sync with phpMyAdmin by updating the main SQL file
    echo "Auto-syncing with phpMyAdmin...\n";

    // Export current database structure to update the main SQL file
    $export_sql = "-- Auto-synced database structure\n";
    $export_sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
    $export_sql .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";

    // Get all tables
    $tables_result = $pdo->query("SHOW TABLES");
    $tables = [];
    while ($row = $tables_result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }

    // Add DROP statements
    foreach ($tables as $table) {
        $export_sql .= "DROP TABLE IF EXISTS `$table`;\n";
    }

    $export_sql .= "\nSET FOREIGN_KEY_CHECKS = 1;\n\n";
    $export_sql .= "CREATE DATABASE IF NOT EXISTS `booking_system`;\n";
    $export_sql .= "USE `booking_system`;\n\n";

    // Add CREATE TABLE statements and data
    foreach ($tables as $table) {
        // Get CREATE TABLE statement
        $create_result = $pdo->query("SHOW CREATE TABLE `$table`");
        $create_row = $create_result->fetch(PDO::FETCH_NUM);
        $export_sql .= $create_row[1] . ";\n\n";

        // Get table data (only for small tables)
        $count_result = $pdo->query("SELECT COUNT(*) FROM `$table`");
        $count = $count_result->fetchColumn();

        if ($count > 0 && $count < 100) { // Only export data for small tables
            $data_result = $pdo->query("SELECT * FROM `$table`");
            while ($row = $data_result->fetch(PDO::FETCH_ASSOC)) {
                $columns = array_keys($row);
                $values = array_map(function($val) use ($pdo) {
                    return $val === null ? 'NULL' : $pdo->quote($val);
                }, array_values($row));

                $export_sql .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $values) . ");\n";
            }
            $export_sql .= "\n";
        }
    }

    // Write to main SQL file
    $sql_file = __DIR__ . '/sql/booking_system.sql';
    if (file_put_contents($sql_file, $export_sql)) {
        echo "✅ Main SQL file updated automatically!\n";
        echo "✅ phpMyAdmin will now show the latest structure!\n";
    } else {
        echo "⚠️ Could not update main SQL file, but database is still fixed.\n";
    }

    echo "\n🎉 ALL FIXES COMPLETED SUCCESSFULLY!\n";
    echo "✅ Database fixed and auto-synced with phpMyAdmin!\n";
    echo "✅ No more 'Table doesn't exist' errors!\n";
    echo "✅ All admin pages should work perfectly now!\n";

} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
?>
