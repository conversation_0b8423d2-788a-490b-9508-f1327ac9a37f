<?php
/**
 * AUTO-<PERSON>Y<PERSON> DATABASE WITH PHPMYADMIN
 * Automatically updates the main SQL file with current database structure
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'booking_system';

try {
    echo "🔄 STARTING AUTO-SYNC WITH PHPMYADMIN...\n\n";
    
    // Connect to MySQL
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database successfully.\n";
    
    // 1. First ensure all required tables exist
    echo "🔧 Ensuring all required tables exist...\n";
    
    // Create destinations table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS `destinations` (
        `destination_id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `description` text DEFAULT NULL,
        `location` varchar(255) DEFAULT NULL,
        `price_per_person` decimal(10,2) DEFAULT 0.00,
        `environmental_fee` decimal(10,2) DEFAULT 100.00,
        `status` enum('active','inactive') DEFAULT 'active',
        `created_at` datetime DEFAULT current_timestamp(),
        `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`destination_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");
    
    // Insert default destinations if empty
    $count = $pdo->query("SELECT COUNT(*) FROM destinations")->fetchColumn();
    if ($count == 0) {
        $pdo->exec("INSERT INTO `destinations` (`destination_id`, `name`, `description`, `location`, `price_per_person`, `environmental_fee`, `status`) VALUES 
        (1, 'Gigantes Island', 'Beautiful island destination with crystal clear waters and white sand beaches', 'Carles, Iloilo', 0.00, 100.00, 'active'),
        (2, 'Antonia Beach', 'Pristine beach with excellent snorkeling spots', 'Carles, Iloilo', 0.00, 100.00, 'active'),
        (3, 'Cabugao Gamay Island', 'Small island perfect for day trips and swimming', 'Carles, Iloilo', 0.00, 100.00, 'active'),
        (4, 'Tangke Saltwater Lagoon', 'Natural saltwater lagoon with unique rock formations', 'Carles, Iloilo', 0.00, 100.00, 'active'),
        (5, 'Bantigue Sand Bar', 'Stunning sandbar that appears during low tide', 'Carles, Iloilo', 0.00, 100.00, 'active')");
        echo "✅ Added default destinations.\n";
    }
    
    // Create booking_passengers table if not exists
    $pdo->exec("CREATE TABLE IF NOT EXISTS `booking_passengers` (
        `passenger_id` int(11) NOT NULL AUTO_INCREMENT,
        `booking_id` int(11) NOT NULL,
        `first_name` varchar(100) NOT NULL,
        `last_name` varchar(100) NOT NULL,
        `age` int(11) NOT NULL,
        `sex` enum('Male','Female') NOT NULL,
        `complete_address` text DEFAULT NULL,
        `contact_number` varchar(20) DEFAULT NULL,
        `passenger_type` enum('main_booker','companion','regular','discounted','children','infants') DEFAULT 'companion',
        `is_main_contact` tinyint(1) DEFAULT 0,
        `created_at` datetime DEFAULT current_timestamp(),
        PRIMARY KEY (`passenger_id`),
        KEY `booking_id` (`booking_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci");
    
    // Add missing columns to bookings table
    $columns_to_add = [
        'suffix' => "ALTER TABLE `bookings` ADD COLUMN `suffix` varchar(10) DEFAULT NULL AFTER `last_name`",
        'total_passengers' => "ALTER TABLE `bookings` ADD COLUMN `total_passengers` int(11) DEFAULT 1 AFTER `no_of_pax`",
        'payment_proof' => "ALTER TABLE `bookings` ADD COLUMN `payment_proof` varchar(255) DEFAULT NULL AFTER `payment_method`",
        'is_today_booking' => "ALTER TABLE `bookings` ADD COLUMN `is_today_booking` tinyint(1) DEFAULT 0 AFTER `booking_status`",
        'passenger_details' => "ALTER TABLE `bookings` ADD COLUMN `passenger_details` longtext DEFAULT NULL AFTER `destination_id`"
    ];
    
    foreach ($columns_to_add as $column => $sql) {
        try {
            $check = $pdo->query("SHOW COLUMNS FROM bookings LIKE '$column'")->rowCount();
            if ($check == 0) {
                $pdo->exec($sql);
                echo "✅ Added column: $column\n";
            }
        } catch (PDOException $e) {
            // Column might already exist, continue
        }
    }
    
    echo "✅ All required tables and columns verified.\n\n";
    
    // 2. Now export current database structure
    echo "📤 Exporting current database structure...\n";
    
    $export_sql = "-- Auto-synced database structure\n";
    $export_sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n\n";
    $export_sql .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
    
    // Get all tables
    $tables_result = $pdo->query("SHOW TABLES");
    $tables = [];
    while ($row = $tables_result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    
    // Add DROP statements
    foreach ($tables as $table) {
        $export_sql .= "DROP TABLE IF EXISTS `$table`;\n";
    }
    
    $export_sql .= "\nSET FOREIGN_KEY_CHECKS = 1;\n\n";
    $export_sql .= "CREATE DATABASE IF NOT EXISTS `booking_system`;\n";
    $export_sql .= "USE `booking_system`;\n\n";
    
    // Add CREATE TABLE statements
    foreach ($tables as $table) {
        $create_result = $pdo->query("SHOW CREATE TABLE `$table`");
        $create_row = $create_result->fetch(PDO::FETCH_NUM);
        $export_sql .= $create_row[1] . ";\n\n";
    }
    
    echo "✅ Database structure exported.\n";
    
    // 3. Write to main SQL file
    echo "💾 Updating main SQL file...\n";
    $sql_file = __DIR__ . '/sql/booking_system.sql';
    
    if (file_put_contents($sql_file, $export_sql)) {
        echo "✅ Main SQL file updated successfully!\n";
    } else {
        echo "⚠️ Could not update main SQL file.\n";
    }
    
    echo "\n🎉 AUTO-SYNC COMPLETED SUCCESSFULLY!\n";
    echo "✅ Database structure synced with phpMyAdmin!\n";
    echo "✅ All admin pages should work without errors!\n";
    echo "✅ No more 'Table doesn't exist' errors!\n\n";
    
    echo "📋 SUMMARY:\n";
    echo "- destinations table: ✅ Created with 5 default destinations\n";
    echo "- booking_passengers table: ✅ Created for passenger details\n";
    echo "- bookings table: ✅ Added missing columns (suffix, total_passengers, etc.)\n";
    echo "- booking_system.sql: ✅ Updated with current structure\n";
    echo "- phpMyAdmin: ✅ Will show latest database structure\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
?>
