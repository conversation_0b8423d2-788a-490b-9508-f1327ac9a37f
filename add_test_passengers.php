<?php
// Add test passenger data to existing bookings
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'booking_system';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "🔧 ADDING TEST PASSENGER DATA...\n\n";
    
    // Get existing bookings without passenger data
    $bookings = $pdo->query("SELECT booking_id, booking_code, first_name, last_name, age, sex, contact_number, address, no_of_pax FROM bookings ORDER BY booking_id DESC LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($bookings as $booking) {
        echo "📋 Adding passengers for booking: {$booking['booking_code']}\n";
        
        // Add main booker as first passenger
        $pdo->exec("INSERT INTO booking_passengers (
            booking_id, first_name, last_name, age, sex, complete_address, contact_number, passenger_type, is_main_contact
        ) VALUES (
            {$booking['booking_id']}, 
            '{$booking['first_name']}', 
            '{$booking['last_name']}', 
            {$booking['age']}, 
            '{$booking['sex']}', 
            '{$booking['address']}', 
            '{$booking['contact_number']}', 
            'main_booker', 
            1
        )");
        
        echo "  ✅ Added main booker: {$booking['first_name']} {$booking['last_name']}\n";
        
        // Add companion passengers based on no_of_pax
        $total_pax = $booking['no_of_pax'];
        $companion_names = [
            ['Maria', 'Santos', 25, 'Female'],
            ['Jose', 'Reyes', 30, 'Male'],
            ['Ana', 'Garcia', 22, 'Female'],
            ['Pedro', 'Lopez', 35, 'Male'],
            ['Carmen', 'Torres', 28, 'Female'],
            ['Miguel', 'Flores', 32, 'Male'],
            ['Rosa', 'Mendoza', 26, 'Female'],
            ['Carlos', 'Herrera', 29, 'Male'],
            ['Elena', 'Jimenez', 24, 'Female'],
            ['Ricardo', 'Morales', 31, 'Male']
        ];
        
        for ($i = 1; $i < $total_pax && $i < 11; $i++) {
            $companion = $companion_names[$i - 1];
            
            // Determine passenger type based on age
            $passenger_type = 'regular';
            if ($companion[2] <= 5) {
                $passenger_type = 'infants';
            } elseif ($companion[2] <= 12) {
                $passenger_type = 'children';
            } elseif ($companion[2] >= 60) {
                $passenger_type = 'discounted';
            }
            
            $pdo->exec("INSERT INTO booking_passengers (
                booking_id, first_name, last_name, age, sex, complete_address, contact_number, passenger_type, is_main_contact
            ) VALUES (
                {$booking['booking_id']}, 
                '{$companion[0]}', 
                '{$companion[1]}', 
                {$companion[2]}, 
                '{$companion[3]}', 
                'Iloilo City, Philippines', 
                '09' . rand(100000000, 999999999), 
                '$passenger_type', 
                0
            )");
            
            echo "  ✅ Added companion: {$companion[0]} {$companion[1]} ({$companion[2]} years old, $passenger_type)\n";
        }
        
        echo "\n";
    }
    
    // Check results
    $total_passengers = $pdo->query("SELECT COUNT(*) FROM booking_passengers")->fetchColumn();
    echo "🎉 SUCCESS! Total passengers in database: $total_passengers\n\n";
    
    // Show sample data
    echo "📋 Sample passenger data:\n";
    $sample = $pdo->query("
        SELECT bp.*, b.booking_code 
        FROM booking_passengers bp 
        JOIN bookings b ON bp.booking_id = b.booking_id 
        ORDER BY bp.booking_id DESC, bp.is_main_contact DESC 
        LIMIT 10
    ")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sample as $passenger) {
        $main_contact = $passenger['is_main_contact'] ? ' (MAIN CONTACT)' : '';
        echo "- {$passenger['booking_code']}: {$passenger['first_name']} {$passenger['last_name']} ({$passenger['age']}, {$passenger['sex']}, {$passenger['passenger_type']})$main_contact\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}
?>
